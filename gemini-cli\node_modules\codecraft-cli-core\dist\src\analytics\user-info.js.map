{"version": 3, "file": "user-info.js", "sourceRoot": "", "sources": ["../../../src/analytics/user-info.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAWH;;GAEG;AACH,MAAM,aAAa,GAAG,IAAI,GAAG,EAAiD,CAAC;AAC/E,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;AAElD;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,UAAwB;IACxD,IAAI,CAAC;QACH,wDAAwD;QACxD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,UAAU,CAAC,cAAc,EAAE,CAAC;QACpD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,oBAAoB;QACpB,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,kCAAkC;QAC3E,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,cAAc,EAAE,CAAC;YAC7D,OAAO,MAAM,CAAC,IAAI,CAAC;QACrB,CAAC;QAED,iCAAiC;QACjC,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC;YACxC,GAAG,EAAE,+CAA+C;SACrD,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAgB,CAAC;QAE3C,mBAAmB;QACnB,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE;YAC1B,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;QAEH,6BAA6B;QAC7B,YAAY,EAAE,CAAC;QAEf,OAAO,QAAQ,CAAC;IAClB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,YAAY,CAAC,UAAwB;IACzD,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,UAAU,CAAC,CAAC;IAC/C,OAAO,QAAQ,EAAE,KAAK,IAAI,IAAI,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,SAAS,YAAY;IACnB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;QACnD,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,cAAc,EAAE,CAAC;YAC3C,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB;IAChC,aAAa,CAAC,KAAK,EAAE,CAAC;AACxB,CAAC"}