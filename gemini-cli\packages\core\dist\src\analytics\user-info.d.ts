/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { OAuth2Client } from 'google-auth-library';
export interface UserInfo {
    email?: string;
    name?: string;
    picture?: string;
    verified_email?: boolean;
}
/**
 * Extract user email and profile information from OAuth2Client
 * Uses Google's userinfo API with the existing OAuth scopes
 */
export declare function getUserInfo(authClient: OAuth2Client): Promise<UserInfo | null>;
/**
 * Extract just the email address from OAuth2Client
 */
export declare function getUserEmail(authClient: OAuth2Client): Promise<string | null>;
/**
 * Clear the user info cache (useful for testing or logout)
 */
export declare function clearUserInfoCache(): void;
