{"version": 3, "file": "loggers.js", "sourceRoot": "", "sources": ["../../../src/telemetry/loggers.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,IAAI,EAA4B,MAAM,yBAAyB,CAAC;AACzE,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAEzE,OAAO,EACL,eAAe,EACf,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,YAAY,GACb,MAAM,gBAAgB,CAAC;AASxB,OAAO,EACL,qBAAqB,EACrB,uBAAuB,EACvB,wBAAwB,EACxB,qBAAqB,GACtB,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,yBAAyB,EAAE,MAAM,UAAU,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,MAAM,sCAAsC,CAAC;AACtE,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAE1D,oCAAoC;AACpC,IAAI,gBAAgB,GAA4B,IAAI,CAAC;AAErD,MAAM,oBAAoB,GAAG,CAAC,MAAc,EAAW,EAAE,CACvD,MAAM,CAAC,6BAA6B,EAAE,CAAC;AAEzC,SAAS,mBAAmB,CAAC,MAAc;IACzC,OAAO;QACL,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE;KACpC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,MAAc;IACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEhD,4DAA4D;QAC5D,cAAc,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACjC,IAAI,UAAU,EAAE,CAAC;gBACf,gBAAgB,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACf,kDAAkD;YAClD,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,MAAc,EACd,KAAwB;IAExB,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAC;IAChE,IAAI,CAAC,yBAAyB,EAAE;QAAE,OAAO;IAEzC,MAAM,UAAU,GAAkB;QAChC,GAAG,mBAAmB,CAAC,MAAM,CAAC;QAC9B,YAAY,EAAE,gBAAgB;QAC9B,iBAAiB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QAC3C,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,eAAe,EAAE,KAAK,CAAC,eAAe;QACtC,eAAe,EAAE,KAAK,CAAC,eAAe;QACtC,kBAAkB,EAAE,KAAK,CAAC,kBAAkB;QAC5C,aAAa,EAAE,KAAK,CAAC,aAAa;QAClC,eAAe,EAAE,KAAK,CAAC,eAAe;QACtC,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;QAC1C,wBAAwB,EAAE,KAAK,CAAC,kCAAkC;QAClE,iCAAiC,EAAE,KAAK,CAAC,iCAAiC;QAC1E,UAAU,EAAE,KAAK,CAAC,aAAa;QAC/B,WAAW,EAAE,KAAK,CAAC,WAAW;KAC/B,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAc;QAC3B,IAAI,EAAE,2BAA2B;QACjC,UAAU;KACX,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzB,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,MAAc,EAAE,KAAsB;IAClE,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAE7D,iDAAiD;IACjD,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC9C,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAEpC,kCAAkC;QAClC,SAAS,CAAC,aAAa,CAAC,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACnF,qEAAqE;YACrE,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kDAAkD;QAClD,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED,IAAI,CAAC,yBAAyB,EAAE;QAAE,OAAO;IAEzC,MAAM,UAAU,GAAkB;QAChC,GAAG,mBAAmB,CAAC,MAAM,CAAC;QAC9B,YAAY,EAAE,iBAAiB;QAC/B,iBAAiB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QAC3C,aAAa,EAAE,KAAK,CAAC,aAAa;KACnC,CAAC;IAEF,IAAI,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC;QACjC,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACnC,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAc;QAC3B,IAAI,EAAE,wBAAwB,KAAK,CAAC,aAAa,GAAG;QACpD,UAAU;KACX,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzB,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,MAAc,EAAE,KAAoB;IAC9D,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC5D,IAAI,CAAC,yBAAyB,EAAE;QAAE,OAAO;IAEzC,MAAM,UAAU,GAAkB;QAChC,GAAG,mBAAmB,CAAC,MAAM,CAAC;QAC9B,GAAG,KAAK;QACR,YAAY,EAAE,eAAe;QAC7B,iBAAiB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QAC3C,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;KAC5D,CAAC;IACF,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,UAAU,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;QAC1C,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACrB,UAAU,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAc;QAC3B,IAAI,EAAE,cAAc,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,cAAc,KAAK,CAAC,OAAO,eAAe,KAAK,CAAC,WAAW,KAAK;QAC/J,UAAU;KACX,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,qBAAqB,CACnB,MAAM,EACN,KAAK,CAAC,aAAa,EACnB,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,QAAQ,CACf,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,MAAc,EAAE,KAAsB;IAClE,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC9D,IAAI,CAAC,yBAAyB,EAAE;QAAE,OAAO;IAEzC,MAAM,UAAU,GAAkB;QAChC,GAAG,mBAAmB,CAAC,MAAM,CAAC;QAC9B,GAAG,KAAK;QACR,YAAY,EAAE,iBAAiB;QAC/B,iBAAiB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KAC5C,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAc;QAC3B,IAAI,EAAE,kBAAkB,KAAK,CAAC,KAAK,GAAG;QACtC,UAAU;KACX,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzB,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,MAAc,EAAE,KAAoB;IAC9D,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAC5D,IAAI,CAAC,yBAAyB,EAAE;QAAE,OAAO;IAEzC,MAAM,UAAU,GAAkB;QAChC,GAAG,mBAAmB,CAAC,MAAM,CAAC;QAC9B,GAAG,KAAK;QACR,YAAY,EAAE,eAAe;QAC7B,iBAAiB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QAC3C,CAAC,eAAe,CAAC,EAAE,KAAK,CAAC,KAAK;QAC9B,UAAU,EAAE,KAAK,CAAC,KAAK;QACvB,QAAQ,EAAE,KAAK,CAAC,WAAW;KAC5B,CAAC;IAEF,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;QACrB,UAAU,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;IAC9C,CAAC;IACD,IAAI,OAAO,KAAK,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;QAC1C,UAAU,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;IACtE,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAc;QAC3B,IAAI,EAAE,iBAAiB,KAAK,CAAC,KAAK,YAAY,KAAK,CAAC,KAAK,eAAe,KAAK,CAAC,WAAW,KAAK;QAC9F,UAAU;KACX,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,qBAAqB,CACnB,MAAM,EACN,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,UAAU,CACjB,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,MAAc,EAAE,KAAuB;IACpE,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAE/D,gDAAgD;IAChD,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC9C,MAAM,WAAW,GAAG,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,CAAC;YACjE,CAAC,KAAK,CAAC,0BAA0B,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,oBAAoB,IAAI,CAAC,CAAC;YAC3E,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC;QAEjD,sEAAsE;QACtE,SAAS,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC1D,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,CAAC,yBAAyB,EAAE;QAAE,OAAO;IACzC,MAAM,UAAU,GAAkB;QAChC,GAAG,mBAAmB,CAAC,MAAM,CAAC;QAC9B,GAAG,KAAK;QACR,YAAY,EAAE,kBAAkB;QAChC,iBAAiB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KAC5C,CAAC;IACF,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;QACxB,UAAU,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;IACjD,CAAC;IACD,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,UAAU,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;IAC5C,CAAC;SAAM,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;QAC7B,IAAI,OAAO,KAAK,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YAC1C,UAAU,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;QACtE,CAAC;IACH,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAc;QAC3B,IAAI,EAAE,qBAAqB,KAAK,CAAC,KAAK,aAAa,KAAK,CAAC,WAAW,IAAI,KAAK,eAAe,KAAK,CAAC,WAAW,KAAK;QAClH,UAAU;KACX,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvB,wBAAwB,CACtB,MAAM,EACN,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACZ,CAAC;IACF,uBAAuB,CACrB,MAAM,EACN,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,iBAAiB,EACvB,OAAO,CACR,CAAC;IACF,uBAAuB,CACrB,MAAM,EACN,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,kBAAkB,EACxB,QAAQ,CACT,CAAC;IACF,uBAAuB,CACrB,MAAM,EACN,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,0BAA0B,EAChC,OAAO,CACR,CAAC;IACF,uBAAuB,CACrB,MAAM,EACN,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,oBAAoB,EAC1B,SAAS,CACV,CAAC;IACF,uBAAuB,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;AAC/E,CAAC"}