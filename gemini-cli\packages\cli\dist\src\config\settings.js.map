{"version": 3, "file": "settings.js", "sourceRoot": "", "sources": ["../../../src/config/settings.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC;AAC7B,OAAO,EAEL,eAAe,GAIhB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,iBAAiB,MAAM,qBAAqB,CAAC;AACpD,OAAO,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAC7D,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AAEtD,MAAM,CAAC,MAAM,uBAAuB,GAAG,SAAS,CAAC;AACjD,MAAM,CAAC,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,uBAAuB,CAAC,CAAC;AAC/E,MAAM,CAAC,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;AAEhF,MAAM,CAAN,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,6BAAa,CAAA;IACb,uCAAuB,CAAA;AACzB,CAAC,EAHW,YAAY,KAAZ,YAAY,QAGvB;AAmDD,MAAM,OAAO,cAAc;IACzB,YACE,IAAkB,EAClB,SAAuB,EACvB,MAAuB;QAEvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC9C,CAAC;IAEQ,IAAI,CAAe;IACnB,SAAS,CAAe;IACxB,MAAM,CAAkB;IAEzB,OAAO,CAAW;IAE1B,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEO,qBAAqB;QAC3B,OAAO;YACL,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;YACrB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ;SAC3B,CAAC;IACJ,CAAC;IAED,QAAQ,CAAC,KAAmB;QAC1B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,YAAY,CAAC,IAAI;gBACpB,OAAO,IAAI,CAAC,IAAI,CAAC;YACnB,KAAK,YAAY,CAAC,SAAS;gBACzB,OAAO,IAAI,CAAC,SAAS,CAAC;YACxB;gBACE,MAAM,IAAI,KAAK,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,QAAQ,CACN,KAAmB,EACnB,GAAmB,EACnB,KAA2D;QAE3D,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC1C,2EAA2E;QAC3E,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC5C,YAAY,CAAC,YAAY,CAAC,CAAC;IAC7B,CAAC;CACF;AAED,SAAS,sBAAsB,CAAC,KAAa;IAC3C,MAAM,WAAW,GAAG,wBAAwB,CAAC,CAAC,gCAAgC;IAC9E,OAAO,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAC9D,MAAM,OAAO,GAAG,QAAQ,IAAI,QAAQ,CAAC;QACrC,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YACvE,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;QAC/B,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,sBAAsB,CAAI,GAAM;IACvC,IACE,GAAG,KAAK,IAAI;QACZ,GAAG,KAAK,SAAS;QACjB,OAAO,GAAG,KAAK,SAAS;QACxB,OAAO,GAAG,KAAK,QAAQ,EACvB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,sBAAsB,CAAC,GAAG,CAAiB,CAAC;IACrD,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAiB,CAAC;IACzE,CAAC;IAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAG,EAAE,GAAG,GAAG,EAAO,CAAC;QAC/B,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC;gBACtD,MAAM,CAAC,GAAG,CAAC,GAAG,sBAAsB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,YAAoB;IAC/C,IAAI,YAAY,GAAa,EAAE,CAAC;IAChC,IAAI,iBAAiB,GAAa,EAAE,CAAC;IACrC,MAAM,cAAc,GAAoB,EAAE,CAAC;IAE3C,qBAAqB;IACrB,IAAI,CAAC;QACH,IAAI,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACtC,MAAM,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;YACjE,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CACnC,iBAAiB,CAAC,WAAW,CAAC,CACnB,CAAC;YACd,YAAY,GAAG,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;YAC1D,6BAA6B;YAC7B,IAAI,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;gBACtD,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;YACzC,CAAC;iBAAM,IAAI,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACjE,YAAY,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,cAAc,CAAC,IAAI,CAAC;YAClB,OAAO,EAAE,eAAe,CAAC,KAAK,CAAC;YAC/B,IAAI,EAAE,kBAAkB;SACzB,CAAC,CAAC;IACL,CAAC;IAED,MAAM,qBAAqB,GAAG,IAAI,CAAC,IAAI,CACrC,YAAY,EACZ,uBAAuB,EACvB,eAAe,CAChB,CAAC;IAEF,0BAA0B;IAC1B,IAAI,CAAC;QACH,IAAI,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACzC,MAAM,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;YACvE,MAAM,uBAAuB,GAAG,IAAI,CAAC,KAAK,CACxC,iBAAiB,CAAC,cAAc,CAAC,CACtB,CAAC;YACd,iBAAiB,GAAG,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;YACpE,IAAI,iBAAiB,CAAC,KAAK,IAAI,iBAAiB,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;gBAChE,iBAAiB,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;YAC9C,CAAC;iBAAM,IACL,iBAAiB,CAAC,KAAK;gBACvB,iBAAiB,CAAC,KAAK,KAAK,QAAQ,EACpC,CAAC;gBACD,iBAAiB,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC;YAC7C,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,cAAc,CAAC,IAAI,CAAC;YAClB,OAAO,EAAE,eAAe,CAAC,KAAK,CAAC;YAC/B,IAAI,EAAE,qBAAqB;SAC5B,CAAC,CAAC;IACL,CAAC;IAED,OAAO,IAAI,cAAc,CACvB;QACE,IAAI,EAAE,kBAAkB;QACxB,QAAQ,EAAE,YAAY;KACvB,EACD;QACE,IAAI,EAAE,qBAAqB;QAC3B,QAAQ,EAAE,iBAAiB;KAC5B,EACD,cAAc,CACf,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,YAA0B;IACrD,IAAI,CAAC;QACH,8BAA8B;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC7C,CAAC;QAED,EAAE,CAAC,aAAa,CACd,YAAY,CAAC,IAAI,EACjB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,EAC9C,OAAO,CACR,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC"}