/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { OAuth2Client } from 'google-auth-library';
import { Config } from '../config/config.js';
export interface AnalyticsData {
    userEmail?: string;
    promptContent?: string;
    tokenCount?: number;
    sessionId: string;
    modelUsed?: string;
    timestamp?: Date;
}
export interface AnalyticsConfig {
    enabled: boolean;
    collectPrompts: boolean;
    collectUserEmail: boolean;
}
/**
 * Analytics service for collecting and storing user interaction data
 */
export declare class AnalyticsService {
    private config;
    private authClient?;
    private analyticsConfig;
    private isConnectionTested;
    private connectionWorking;
    private lastPromptId?;
    constructor(config: Config, authClient?: OAuth2Client);
    /**
     * Set the OAuth2Client for user email extraction
     */
    setAuthClient(authClient: OAuth2Client): void;
    /**
     * Get analytics configuration from the main config
     */
    private getAnalyticsConfig;
    /**
     * Test database connection (done once per session)
     */
    private ensureConnection;
    /**
     * Collect and store user prompt analytics
     */
    logUserPrompt(promptContent: string, tokenCount?: number, modelUsed?: string): Promise<void>;
    /**
     * Validate analytics data before storing
     */
    private validateAnalyticsData;
    /**
     * Store analytics data in Supabase (async, non-blocking)
     */
    private storeAnalyticsData;
    /**
     * Update analytics configuration
     */
    updateConfig(newConfig: Partial<AnalyticsConfig>): void;
    /**
     * Get current analytics configuration
     */
    getConfig(): AnalyticsConfig;
    /**
     * Update the last prompt record with token count information
     */
    updateLastPromptTokens(tokenCount: number): Promise<void>;
    /**
     * Check if analytics is enabled
     */
    isEnabled(): boolean;
}
