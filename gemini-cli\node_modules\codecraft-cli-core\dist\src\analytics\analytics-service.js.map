{"version": 3, "file": "analytics-service.js", "sourceRoot": "", "sources": ["../../../src/analytics/analytics-service.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,iBAAiB,EAAuB,sBAAsB,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjH,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAkB9C;;GAEG;AACH,MAAM,OAAO,gBAAgB;IACnB,MAAM,CAAS;IACf,UAAU,CAAgB;IAC1B,eAAe,CAAkB;IACjC,kBAAkB,GAAG,KAAK,CAAC;IAC3B,iBAAiB,GAAG,KAAK,CAAC;IAC1B,YAAY,CAAU;IAE9B,YAAY,MAAc,EAAE,UAAyB;QACnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAwB;QACpC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,gEAAgE;QAChE,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;QACjD,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;QAChE,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC;QAE5D,OAAO;YACL,OAAO,EAAE,UAAU,KAAK,OAAO,EAAE,gDAAgD;YACjF,cAAc,EAAE,iBAAiB,KAAK,OAAO,EAAE,gDAAgD;YAC/F,gBAAgB,EAAE,eAAe,KAAK,OAAO,EAAE,gDAAgD;SAChG,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC5B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,iBAAiB,GAAG,MAAM,sBAAsB,EAAE,CAAC;QAExD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;QACrG,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,aAAqB,EACrB,UAAmB,EACnB,SAAkB;QAElB,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,aAAa,GAAkB;gBACnC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACrC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,8CAA8C;YAC9C,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC;gBACxC,aAAa,CAAC,aAAa,GAAG,aAAa,CAAC;YAC9C,CAAC;YAED,8BAA8B;YAC9B,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;gBAC7B,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;YACxC,CAAC;YAED,oCAAoC;YACpC,IAAI,SAAS,EAAE,CAAC;gBACd,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC;YACtC,CAAC;YAED,uEAAuE;YACvE,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC7D,IAAI,CAAC;oBACH,aAAa,CAAC,SAAS,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC;gBAC7E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,4DAA4D;oBAC5D,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC;YAED,mCAAmC;YACnC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACnD,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,IAAmB;QAC/C,yBAAyB;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChG,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oCAAoC;QACpC,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACzD,MAAM,UAAU,GAAG,4BAA4B,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC;YAClG,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mEAAmE;QACnE,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC,CAAC,cAAc;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,IAAmB;QAClD,sBAAsB;QACtB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,iBAAiB,EAAE,CAAC;QAErC,MAAM,MAAM,GAAwB;YAClC,UAAU,EAAE,IAAI,CAAC,SAAS;YAC1B,cAAc,EAAE,IAAI,CAAC,aAAa;YAClC,WAAW,EAAE,IAAI,CAAC,UAAU;YAC5B,UAAU,EAAE,IAAI,CAAC,SAAS;YAC1B,UAAU,EAAE,IAAI,CAAC,SAAS;YAC1B,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE;SACzC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,KAAK,IAAI,EAAE;YACxC,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBACjD,IAAI,CAAC,gBAAgB,CAAC;iBACtB,MAAM,CAAC,MAAM,CAAC;iBACd,MAAM,CAAC,IAAI,CAAC;iBACZ,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,OAAO,YAAY,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,2CAA2C;QAC3C,IAAI,MAAM,EAAE,EAAE,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAmC;QAC9C,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,SAAS,EAAE,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QAC7C,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YACxD,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,iBAAiB,EAAE,CAAC;YAErC,MAAM,SAAS,CAAC,KAAK,IAAI,EAAE;gBACzB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;qBAC7B,IAAI,CAAC,gBAAgB,CAAC;qBACtB,MAAM,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;qBACnC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBAE/B,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;IACtC,CAAC;CACF"}