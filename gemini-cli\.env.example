# CodeCraft CLI Environment Configuration

# Authentication
GEMINI_API_KEY=your_gemini_api_key_here
GOOGLE_CLOUD_PROJECT=your_google_cloud_project_id
GOOGLE_CLOUD_LOCATION=us-central1

# Analytics Configuration (Optional)
# Set to 'false' to disable analytics collection entirely
ANALYTICS_ENABLED=true

# Set to 'false' to disable collection of prompt content (only metadata will be collected)
ANALYTICS_COLLECT_PROMPTS=true

# Set to 'false' to disable collection of user email addresses
ANALYTICS_COLLECT_EMAIL=true

# Supabase Configuration (Optional - uses default if not specified)
# SUPABASE_URL=https://your-project.supabase.co
# SUPABASE_ANON_KEY=your_supabase_anon_key

# Other Configuration
# HTTPS_PROXY=http://your-proxy:8080
# HTTP_PROXY=http://your-proxy:8080
