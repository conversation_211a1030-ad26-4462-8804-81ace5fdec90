{"name": "@supabase/storage-js", "version": "2.10.4", "description": "Isomorphic storage client for Supabase.", "keywords": ["javascript", "typescript", "supabase"], "homepage": "https://github.com/supabase/storage-js", "bugs": "https://github.com/supabase/storage-js/issues", "license": "MIT", "author": "Supabase", "files": ["dist", "src"], "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "sideEffects": false, "repository": "supabase/storage-js", "scripts": {"clean": "rimraf dist docs/v2", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "genversion src/lib/version.ts --es6 && run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "build:umd": "webpack", "types-generate": "dts-gen -m '@supabase/storage-js' -s", "test": "run-s test:clean test:infra test:suite test:clean", "test:suite": "jest --runInBand --coverage", "test:infra": "cd infra && docker compose down && docker compose up -d --build && sleep 10", "test:clean": "cd infra && docker compose down --remove-orphans", "docs": "typedoc --entryPoints src/index.ts --out docs/v2 --entryPoints src/packages/* --excludePrivate --excludeProtected", "docs:json": "typedoc --json docs/v2/spec.json --entryPoints src/index.ts --entryPoints src/packages/* --excludePrivate --excludeExternals --excludeProtected"}, "dependencies": {"@supabase/node-fetch": "^2.6.14"}, "devDependencies": {"@types/jest": "^26.0.13", "form-data": "^4.0.0", "genversion": "^3.0.1", "husky": "^4.3.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.5.0", "npm-run-all": "^4.1.5", "prettier": "^2.1.2", "pretty-quick": "^3.1.0", "rimraf": "^3.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "ts-jest": "^29.0.0", "ts-loader": "^9.4.2", "typedoc": "^0.22.16", "typescript": "^4.6.3", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "jsdelivr": "dist/umd/supabase.js", "unpkg": "dist/umd/supabase.js", "publishConfig": {"access": "public"}}