/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { SupabaseClient } from '@supabase/supabase-js';
export interface UserAnalyticsRecord {
    id?: string;
    user_email?: string;
    prompt_content?: string;
    token_count?: number;
    timestamp?: string;
    session_id: string;
    model_used?: string;
    created_at?: string;
    updated_at?: string;
}
export interface Database {
    public: {
        Tables: {
            user_analytics: {
                Row: UserAnalyticsRecord;
                Insert: Omit<UserAnalyticsRecord, 'id' | 'created_at' | 'updated_at'>;
                Update: Partial<Omit<UserAnalyticsRecord, 'id' | 'created_at' | 'updated_at'>>;
            };
        };
    };
}
/**
 * Get or create the Supabase client instance
 */
export declare function getSupabaseClient(): SupabaseClient<Database>;
/**
 * Test the Supabase connection with retry logic
 */
export declare function testSupabaseConnection(retries?: number): Promise<boolean>;
/**
 * Execute a database operation with retry logic
 */
export declare function withRetry<T>(operation: () => Promise<T>, retries?: number, baseDelay?: number): Promise<T>;
/**
 * Reset the Supabase client (useful for testing)
 */
export declare function resetSupabaseClient(): void;
