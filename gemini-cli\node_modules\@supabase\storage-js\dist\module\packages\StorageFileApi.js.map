{"version": 3, "file": "StorageFileApi.js", "sourceRoot": "", "sources": ["../../../src/packages/StorageFileApi.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAA;AACjF,OAAO,EAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,cAAc,CAAA;AAClE,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAY/D,MAAM,sBAAsB,GAAG;IAC7B,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,CAAC;IACT,MAAM,EAAE;QACN,MAAM,EAAE,MAAM;QACd,KAAK,EAAE,KAAK;KACb;CACF,CAAA;AAED,MAAM,oBAAoB,GAAgB;IACxC,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,0BAA0B;IACvC,MAAM,EAAE,KAAK;CACd,CAAA;AAcD,MAAM,CAAC,OAAO,OAAO,cAAc;IAMjC,YACE,GAAW,EACX,UAAqC,EAAE,EACvC,QAAiB,EACjB,KAAa;QAEb,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAA;IAClC,CAAC;IAED;;;;;;OAMG;IACW,cAAc,CAC1B,MAAsB,EACtB,IAAY,EACZ,QAAkB,EAClB,WAAyB;;YAWzB,IAAI;gBACF,IAAI,IAAI,CAAA;gBACR,MAAM,OAAO,mCAAQ,oBAAoB,GAAK,WAAW,CAAE,CAAA;gBAC3D,IAAI,OAAO,mCACN,IAAI,CAAC,OAAO,GACZ,CAAC,MAAM,KAAK,MAAM,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,MAAiB,CAAC,EAAE,CAAC,CAC5E,CAAA;gBAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;gBAEjC,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,QAAQ,YAAY,IAAI,EAAE;oBAC3D,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAA;oBACrB,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,YAAsB,CAAC,CAAA;oBAC3D,IAAI,QAAQ,EAAE;wBACZ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA;qBACvD;oBACD,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;iBAC1B;qBAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,YAAY,QAAQ,EAAE;oBAC1E,IAAI,GAAG,QAAQ,CAAA;oBACf,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,YAAsB,CAAC,CAAA;oBAC3D,IAAI,QAAQ,EAAE;wBACZ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA;qBACvD;iBACF;qBAAM;oBACL,IAAI,GAAG,QAAQ,CAAA;oBACf,OAAO,CAAC,eAAe,CAAC,GAAG,WAAW,OAAO,CAAC,YAAY,EAAE,CAAA;oBAC5D,OAAO,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC,WAAqB,CAAA;oBAEvD,IAAI,QAAQ,EAAE;wBACZ,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA;qBACrE;iBACF;gBAED,IAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,OAAO,EAAE;oBACxB,OAAO,mCAAQ,OAAO,GAAK,WAAW,CAAC,OAAO,CAAE,CAAA;iBACjD;gBAED,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;gBAChD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;gBAC3C,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAC/C,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,WAAW,KAAK,EAAE,EAC7B,IAAc,kBACZ,OAAO,IAAK,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAC,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAClE,CAAA;gBAED,OAAO;oBACL,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;oBAC1D,KAAK,EAAE,IAAI;iBACZ,CAAA;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACG,MAAM,CACV,IAAY,EACZ,QAAkB,EAClB,WAAyB;;YAWzB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAA;QACjE,CAAC;KAAA;IAED;;;;;OAKG;IACG,iBAAiB,CACrB,IAAY,EACZ,KAAa,EACb,QAAkB,EAClB,WAAyB;;YAEzB,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;YAE3C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,uBAAuB,KAAK,EAAE,CAAC,CAAA;YAC9D,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YAEpC,IAAI;gBACF,IAAI,IAAI,CAAA;gBACR,MAAM,OAAO,mBAAK,MAAM,EAAE,oBAAoB,CAAC,MAAM,IAAK,WAAW,CAAE,CAAA;gBACvE,MAAM,OAAO,mCACR,IAAI,CAAC,OAAO,GACZ,EAAE,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,MAAiB,CAAC,EAAE,CACrD,CAAA;gBAED,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,QAAQ,YAAY,IAAI,EAAE;oBAC3D,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAA;oBACrB,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,YAAsB,CAAC,CAAA;oBAC3D,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;iBAC1B;qBAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,YAAY,QAAQ,EAAE;oBAC1E,IAAI,GAAG,QAAQ,CAAA;oBACf,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,YAAsB,CAAC,CAAA;iBAC5D;qBAAM;oBACL,IAAI,GAAG,QAAQ,CAAA;oBACf,OAAO,CAAC,eAAe,CAAC,GAAG,WAAW,OAAO,CAAC,YAAY,EAAE,CAAA;oBAC5D,OAAO,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC,WAAqB,CAAA;iBACxD;gBAED,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAc,EAAE,EAAE,OAAO,EAAE,CAAC,CAAA;gBAE/E,OAAO;oBACL,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;oBAC7C,KAAK,EAAE,IAAI;iBACZ,CAAA;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACG,qBAAqB,CACzB,IAAY,EACZ,OAA6B;;YAW7B,IAAI;gBACF,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;gBAEpC,MAAM,OAAO,qBAAQ,IAAI,CAAC,OAAO,CAAE,CAAA;gBAEnC,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE;oBACnB,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM,CAAA;iBAC7B;gBAED,MAAM,IAAI,GAAG,MAAM,IAAI,CACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,uBAAuB,KAAK,EAAE,EACzC,EAAE,EACF,EAAE,OAAO,EAAE,CACZ,CAAA;gBAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;gBAExC,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;gBAE3C,IAAI,CAAC,KAAK,EAAE;oBACV,MAAM,IAAI,YAAY,CAAC,0BAA0B,CAAC,CAAA;iBACnD;gBAED,OAAO,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACzE;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACG,MAAM,CACV,IAAY,EACZ,QAUU,EACV,WAAyB;;YAWzB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAA;QAChE,CAAC;KAAA;IAED;;;;;;OAMG;IACG,IAAI,CACR,QAAgB,EAChB,MAAc,EACd,OAA4B;;YAW5B,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAI,CACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,cAAc,EACzB;oBACE,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,QAAQ;oBACnB,cAAc,EAAE,MAAM;oBACtB,iBAAiB,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB;iBAC9C,EACD,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACG,IAAI,CACR,QAAgB,EAChB,MAAc,EACd,OAA4B;;YAW5B,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAI,CACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,cAAc,EACzB;oBACE,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,QAAQ;oBACnB,cAAc,EAAE,MAAM;oBACtB,iBAAiB,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB;iBAC9C,EACD,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACjD;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACG,eAAe,CACnB,IAAY,EACZ,SAAiB,EACjB,OAAuE;;YAWvE,IAAI;gBACF,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;gBAEpC,IAAI,IAAI,GAAG,MAAM,IAAI,CACnB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,gBAAgB,KAAK,EAAE,kBAChC,SAAS,IAAK,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAC,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAC5E,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,MAAM,kBAAkB,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ;oBAC1C,CAAC,CAAC,aAAa,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE;oBAClE,CAAC,CAAC,EAAE,CAAA;gBACN,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,kBAAkB,EAAE,CAAC,CAAA;gBAChF,IAAI,GAAG,EAAE,SAAS,EAAE,CAAA;gBACpB,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;OAMG;IACG,gBAAgB,CACpB,KAAe,EACf,SAAiB,EACjB,OAAwC;;YAWxC,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,IAAI,CACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,gBAAgB,IAAI,CAAC,QAAQ,EAAE,EAC1C,EAAE,SAAS,EAAE,KAAK,EAAE,EACpB,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBAED,MAAM,kBAAkB,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ;oBAC1C,CAAC,CAAC,aAAa,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE;oBAClE,CAAC,CAAC,EAAE,CAAA;gBACN,OAAO;oBACL,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,KAA4B,EAAE,EAAE,CAAC,iCAC5C,KAAK,KACR,SAAS,EAAE,KAAK,CAAC,SAAS;4BACxB,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,kBAAkB,EAAE,CAAC;4BACjE,CAAC,CAAC,IAAI,IACR,CAAC;oBACH,KAAK,EAAE,IAAI;iBACZ,CAAA;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACG,QAAQ,CACZ,IAAY,EACZ,OAA0C;;YAW1C,MAAM,mBAAmB,GAAG,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,CAAA,KAAK,WAAW,CAAA;YACrE,MAAM,UAAU,GAAG,mBAAmB,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,QAAQ,CAAA;YAChF,MAAM,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,EAAE,CAAC,CAAA;YACrF,MAAM,WAAW,GAAG,mBAAmB,CAAC,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;YAExE,IAAI;gBACF,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;gBACtC,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,UAAU,IAAI,KAAK,GAAG,WAAW,EAAE,EAAE;oBACpF,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAA;gBACF,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAA;gBAC7B,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;OAGG;IACG,IAAI,CACR,IAAY;;YAWZ,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAEtC,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,gBAAgB,KAAK,EAAE,EAAE;oBACrE,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;gBAEF,OAAO,EAAE,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAA2B,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC/E;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;OAGG;IACG,MAAM,CACV,IAAY;;YAWZ,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAEtC,IAAI;gBACF,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,WAAW,KAAK,EAAE,EAAE;oBACpD,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;gBAEF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aACnC;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,mBAAmB,EAAE;oBACjE,MAAM,aAAa,GAAI,KAAK,CAAC,aAA+C,CAAA;oBAE5E,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,CAAC,EAAE;wBAC9C,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAA;qBAC9B;iBACF;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACH,YAAY,CACV,IAAY,EACZ,OAAuE;QAEvE,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QACtC,MAAM,YAAY,GAAG,EAAE,CAAA;QAEvB,MAAM,kBAAkB,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ;YAC1C,CAAC,CAAC,YAAY,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE;YACjE,CAAC,CAAC,EAAE,CAAA;QAEN,IAAI,kBAAkB,KAAK,EAAE,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;SACtC;QAED,MAAM,mBAAmB,GAAG,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,CAAA,KAAK,WAAW,CAAA;QACrE,MAAM,UAAU,GAAG,mBAAmB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAA;QAClE,MAAM,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,EAAE,CAAC,CAAA;QAErF,IAAI,mBAAmB,KAAK,EAAE,EAAE;YAC9B,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;SACvC;QAED,IAAI,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACxC,IAAI,WAAW,KAAK,EAAE,EAAE;YACtB,WAAW,GAAG,IAAI,WAAW,EAAE,CAAA;SAChC;QAED,OAAO;YACL,IAAI,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,UAAU,WAAW,KAAK,GAAG,WAAW,EAAE,CAAC,EAAE;SAC1F,CAAA;IACH,CAAC;IAED;;;;OAIG;IACG,MAAM,CACV,KAAe;;YAWf,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,MAAM,CACvB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,WAAW,IAAI,CAAC,QAAQ,EAAE,EACrC,EAAE,QAAQ,EAAE,KAAK,EAAE,EACnB,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAED;;;OAGG;IACH,qBAAqB;IACrB,eAAe;IACf,cAAc;IACd,QAAQ;IACR,uBAAuB;IACvB,oBAAoB;IACpB,QAAQ;IACR,QAAQ;IACR,mBAAmB;IACnB,4BAA4B;IAC5B,QAAQ;IACR,MAAM;IACN,UAAU;IACV,kGAAkG;IAClG,mCAAmC;IACnC,sBAAsB;IACtB,mCAAmC;IACnC,qCAAqC;IACrC,QAAQ;IAER,kBAAkB;IAClB,MAAM;IACN,IAAI;IAEJ;;;;OAIG;IACH,wBAAwB;IACxB,gBAAgB;IAChB,mBAAmB;IACnB,cAAc;IACd,QAAQ;IACR,uBAAuB;IACvB,oBAAoB;IACpB,QAAQ;IACR,QAAQ;IACR,mBAAmB;IACnB,4BAA4B;IAC5B,QAAQ;IACR,MAAM;IACN,UAAU;IACV,+BAA+B;IAC/B,oBAAoB;IACpB,sCAAsC;IACtC,qBAAqB;IACrB,kCAAkC;IAClC,QAAQ;IACR,mCAAmC;IACnC,sBAAsB;IACtB,mCAAmC;IACnC,qCAAqC;IACrC,QAAQ;IAER,kBAAkB;IAClB,MAAM;IACN,IAAI;IAEJ;;;;OAIG;IACG,IAAI,CACR,IAAa,EACb,OAAuB,EACvB,UAA4B;;YAW5B,IAAI;gBACF,MAAM,IAAI,iDAAQ,sBAAsB,GAAK,OAAO,KAAE,MAAM,EAAE,IAAI,IAAI,EAAE,GAAE,CAAA;gBAC1E,MAAM,IAAI,GAAG,MAAM,IAAI,CACrB,IAAI,CAAC,KAAK,EACV,GAAG,IAAI,CAAC,GAAG,gBAAgB,IAAI,CAAC,QAAQ,EAAE,EAC1C,IAAI,EACJ,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,EACzB,UAAU,CACX,CAAA;gBACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;iBAC7B;gBAED,MAAM,KAAK,CAAA;aACZ;QACH,CAAC;KAAA;IAES,cAAc,CAAC,QAA6B;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;IACjC,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;SAC5C;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAA;IACnB,CAAC;IAEO,aAAa,CAAC,IAAY;QAChC,OAAO,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAA;IACvD,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;IAC1D,CAAC;IAEO,0BAA0B,CAAC,SAA2B;QAC5D,MAAM,MAAM,GAAG,EAAE,CAAA;QACjB,IAAI,SAAS,CAAC,KAAK,EAAE;YACnB,MAAM,CAAC,IAAI,CAAC,SAAS,SAAS,CAAC,KAAK,EAAE,CAAC,CAAA;SACxC;QAED,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,UAAU,SAAS,CAAC,MAAM,EAAE,CAAC,CAAA;SAC1C;QAED,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,UAAU,SAAS,CAAC,MAAM,EAAE,CAAC,CAAA;SAC1C;QAED,IAAI,SAAS,CAAC,MAAM,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,UAAU,SAAS,CAAC,MAAM,EAAE,CAAC,CAAA;SAC1C;QAED,IAAI,SAAS,CAAC,OAAO,EAAE;YACrB,MAAM,CAAC,IAAI,CAAC,WAAW,SAAS,CAAC,OAAO,EAAE,CAAC,CAAA;SAC5C;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;CACF"}